module.exports = {
  apps: [
    {
      name: 'smartv-backend',
      script: 'api/simple-server.cjs',
      cwd: '/www/wwwroot/smarttv',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 4000
      },
      log_file: '/www/wwwlogs/smartv-backend.log',
      error_file: '/www/wwwlogs/smartv-backend-error.log',
      out_file: '/www/wwwlogs/smartv-backend-out.log'
    },
    {
      name: 'smartv-proxy',
      script: 'proxy-server.cjs',
      cwd: '/www/wwwroot/smarttv',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '512M',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      log_file: '/www/wwwlogs/smartv-proxy.log',
      error_file: '/www/wwwlogs/smartv-proxy-error.log',
      out_file: '/www/wwwlogs/smartv-proxy-out.log'
    }
  ]
};