#!/bin/bash

# Script de Deploy para Produção - SmartV.shop
# Autor: Sistema SmartV
# Data: $(date)

echo "🚀 Iniciando deploy para produção..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. Limpar cache e arquivos temporários
log_info "Limpando cache e arquivos temporários..."
rm -rf dist/
rm -rf node_modules/.cache/
rm -rf api/node_modules/.cache/
rm -rf .vite/
rm -rf api/.vite/
rm -rf logs/
rm -rf pids/
rm -f api/tsconfig.tsbuildinfo
log_success "Cache limpo!"

# 2. Instalar dependências
log_info "Instalando dependências do frontend..."
npm install --production=false

log_info "Instalando dependências do backend..."
cd api && npm install --production=false && cd ..

# 3. Build do frontend
log_info "Fazendo build do frontend..."
npm run build

# 4. Verificar se o build foi criado
if [ ! -d "dist" ]; then
    log_error "Build falhou! Diretório dist não foi criado."
    exit 1
fi

log_success "Build do frontend concluído!"

# 5. Criar estrutura de diretórios no servidor
log_info "Preparando estrutura para deploy..."

# 6. Criar arquivo de configuração do PM2
cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'smartv-backend',
    script: 'api/proxy-server.cjs',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: '/www/wwwroot/smarttv/logs/err.log',
    out_file: '/www/wwwroot/smarttv/logs/out.log',
    log_file: '/www/wwwroot/smarttv/logs/combined.log',
    time: true
  }]
};
EOF

log_success "Configuração do PM2 criada!"

# 7. Criar script de inicialização
cat > start-server.sh << 'EOF'
#!/bin/bash
echo "🚀 Iniciando SmartV Server..."

# Criar diretório de logs se não existir
mkdir -p logs

# Parar processo anterior se existir
pm2 stop smartv-backend 2>/dev/null || true
pm2 delete smartv-backend 2>/dev/null || true

# Iniciar com PM2
pm2 start ecosystem.config.js

# Salvar configuração do PM2
pm2 save

echo "✅ SmartV Server iniciado com sucesso!"
echo "📊 Para monitorar: pm2 monit"
echo "📋 Para ver logs: pm2 logs smartv-backend"
EOF

chmod +x start-server.sh

# 8. Criar script de parada
cat > stop-server.sh << 'EOF'
#!/bin/bash
echo "🛑 Parando SmartV Server..."

pm2 stop smartv-backend
pm2 delete smartv-backend

echo "✅ SmartV Server parado!"
EOF

chmod +x stop-server.sh

log_success "Scripts de controle criados!"

echo ""
echo "🎉 Deploy preparado com sucesso!"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "1. Faça upload dos arquivos para /www/wwwroot/smarttv/"
echo "2. No servidor, execute: chmod +x *.sh"
echo "3. Execute: ./start-server.sh"
echo "4. Configure o nginx conforme o manual"
echo ""
